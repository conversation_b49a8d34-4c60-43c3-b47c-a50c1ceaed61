# Google Sheets Configuration (New Database Setup)
GOOGLE_SHEETS_SPREADSHEET_ID=your_spreadsheet_id_here
GOOGLE_SERVICE_ACCOUNT_CREDENTIALS={"type":"service_account","project_id":"your_project","private_key_id":"key_id","private_key":"-----B<PERSON>IN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n","client_email":"*********************************************************","client_id":"your_client_id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/your_service_account%40your_project.iam.gserviceaccount.com"}

# Alternative: Path to service account key file (if not using credentials directly)
# GOOGLE_SERVICE_ACCOUNT_KEY_FILE=./path/to/service-account-key.json

# Other existing environment variables
# Add your other environment variables here...