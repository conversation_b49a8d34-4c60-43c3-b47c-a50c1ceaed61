import type { Metadata } from 'next';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/auth-context';
import { DataProvider } from '@/contexts/data-context';
import { AuthWrapper } from '@/components/auth/auth-wrapper';

export const metadata: Metadata = {
  title: 'Quản lý LTSpa',
  description: 'Quản lý hoạt động spa hiệu quả.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="font-body antialiased">
        <AuthProvider>
          <DataProvider>
            <AuthWrapper>
              {children}
            </AuthWrapper>
          </DataProvider>
        </AuthProvider>
        <Toaster />
      </body>
    </html>
  );
}
