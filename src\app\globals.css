@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Sophisticated Spa Theme - Charcoal Gray & Muted Gold */
    --background: 0 0% 98%;
    --foreground: 200 15% 25%;
    --card: 0 0% 100%;
    --card-foreground: 200 15% 25%;
    --popover: 0 0% 100%;
    --popover-foreground: 200 15% 25%;
    --primary: 200 15% 25%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 200 15% 25%;
    --muted: 0 0% 96%;
    --muted-foreground: 200 8% 45%;
    --accent: 30 25% 65%;
    --accent-foreground: 200 15% 25%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 94%;
    --ring: 30 25% 65%;
    --radius: 0.8rem;
    --chart-1: 30 25% 65%;
    --chart-2: 200 15% 35%;
    --chart-3: 200 20% 45%;
    --chart-4: 30 30% 70%;
    --chart-5: 200 10% 55%;
    --sidebar-background: 200 15% 25%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 30 25% 65%;
    --sidebar-primary-foreground: 200 15% 25%;
    --sidebar-accent: 200 20% 35%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 200 20% 30%;
    --sidebar-ring: 30 25% 65%;
  }

  .dark {
    /* Dark Mode - Sophisticated Spa Theme */
    --background: 200 15% 8%;
    --foreground: 0 0% 95%;
    --card: 200 15% 12%;
    --card-foreground: 0 0% 95%;
    --popover: 200 15% 12%;
    --popover-foreground: 0 0% 95%;
    --primary: 30 25% 65%;
    --primary-foreground: 200 15% 15%;
    --secondary: 200 10% 18%;
    --secondary-foreground: 0 0% 95%;
    --muted: 200 10% 18%;
    --muted-foreground: 0 0% 65%;
    --accent: 30 25% 65%;
    --accent-foreground: 200 15% 15%;
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 98%;
    --border: 200 10% 20%;
    --input: 200 10% 20%;
    --ring: 30 25% 65%;
    --radius: 0.8rem;
    --chart-1: 30 25% 65%;
    --chart-2: 200 15% 45%;
    --chart-3: 200 20% 55%;
    --chart-4: 30 30% 70%;
    --chart-5: 200 10% 65%;
    --sidebar-background: 200 15% 10%;
    --sidebar-foreground: 0 0% 90%;
    --sidebar-primary: 30 25% 65%;
    --sidebar-primary-foreground: 200 15% 15%;
    --sidebar-accent: 200 20% 25%;
    --sidebar-accent-foreground: 0 0% 90%;
    --sidebar-border: 200 20% 20%;
    --sidebar-ring: 30 25% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Sophisticated Spa Theme Enhancements */
  .spa-card {
    @apply bg-card border border-border/50 shadow-sm hover:shadow-md transition-all duration-300;
    backdrop-filter: blur(10px);
  }
  
  .spa-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md transition-all duration-200;
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.9) 100%);
  }
  
  .spa-button-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent/90 shadow-sm hover:shadow-md transition-all duration-200;
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent) / 0.9) 100%);
  }
  
  .spa-sidebar {
    @apply bg-sidebar border-r border-sidebar-border/50;
    background: linear-gradient(180deg, hsl(var(--sidebar-background)) 0%, hsl(var(--sidebar-background) / 0.95) 100%);
    backdrop-filter: blur(20px);
  }
  
  .spa-input {
    @apply bg-input/50 border border-border/60 focus:border-ring/60 focus:ring-2 focus:ring-ring/20 transition-all duration-200;
    backdrop-filter: blur(5px);
  }
  
  .spa-badge-accent {
    @apply bg-accent/10 text-accent border border-accent/20;
    backdrop-filter: blur(5px);
  }
  
  .spa-badge-primary {
    @apply bg-primary/10 text-primary border border-primary/20;
    backdrop-filter: blur(5px);
  }
  
  .spa-text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .spa-glass {
    @apply bg-card/80 border border-border/30;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .spa-shadow-elegant {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .spa-shadow-soft {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.08);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.4s ease-out;
  }
  
  /* Mobile-first responsive dialog utilities */
  .mobile-dialog {
    @apply max-w-[95vw] max-h-[90vh] overflow-y-auto;
  }
  
  .mobile-dialog-content {
    @apply p-4 space-y-4;
  }
  
  .mobile-form-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4;
  }
  
  .mobile-button-group {
    @apply flex flex-col sm:flex-row gap-2 sm:gap-4 w-full;
  }
  
  .mobile-button {
    @apply w-full sm:w-auto text-sm;
  }
  
  /* Responsive spacing utilities */
  .mobile-spacing {
    @apply space-y-3 sm:space-y-4;
  }
  
  .mobile-padding {
    @apply p-3 sm:p-4 lg:p-6;
  }
  
  /* Mobile-optimized text sizes */
  .mobile-text-sm {
    @apply text-xs sm:text-sm;
  }
  
  .mobile-text-base {
    @apply text-sm sm:text-base;
  }
  
  .mobile-text-lg {
    @apply text-base sm:text-lg;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
